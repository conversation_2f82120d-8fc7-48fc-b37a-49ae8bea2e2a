<!-- Dashboard Layout -->
<div
  class="p-6 max-w-4xl mx-auto space-y-6"
  *ngIf="tabService.tabsValue.length === 0"
>
  <!-- Welcome State -->
  <div class="card">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        Welcome to NS Drive Dashboard
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Start by creating your first sync operation
      </p>
    </div>

    <div class="mb-6">
      <p class="text-gray-700 dark:text-gray-300 mb-6">
        Sync operations allow you to synchronize files between local directories
        and cloud storage services. Each operation runs independently with its
        own configuration and profile.
      </p>

      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <lucide-icon
            [img]="RefreshCwIcon"
            class="w-6 h-6 text-primary-600"
          ></lucide-icon>
          <span class="text-gray-700 dark:text-gray-300"
            >Real-time synchronization</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <lucide-icon
            [img]="CloudIcon"
            class="w-6 h-6 text-primary-600"
          ></lucide-icon>
          <span class="text-gray-700 dark:text-gray-300"
            >Multiple cloud providers</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <lucide-icon
            [img]="SettingsIcon"
            class="w-6 h-6 text-primary-600"
          ></lucide-icon>
          <span class="text-gray-700 dark:text-gray-300"
            >Customizable profiles</span
          >
        </div>
      </div>
    </div>

    <div class="flex justify-end">
      <button class="btn-primary whitespace-nowrap" (click)="createTab()">
        <div class="flex items-center space-x-2">
          <lucide-icon [img]="PlusIcon" class="w-5 h-5 mr-2"></lucide-icon>
          Create First Operation
        </div>
      </button>
    </div>
  </div>
</div>

<!-- Operations Dashboard -->
<div *ngIf="tabService.tabsValue.length > 0" class="p-6 max-w-6xl mx-auto">
  <div class="card">
    <div class="mb-6">
      <div class="flex items-center space-x-3 mb-2">
        <lucide-icon
          [img]="RefreshCwIcon"
          class="w-6 h-6 text-primary-600"
        ></lucide-icon>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Sync Operations
        </h1>
      </div>
      <p class="text-gray-600 dark:text-gray-400">
        {{ tabService.tabsValue.length }} operation(s) configured
      </p>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
      <div class="flex items-center justify-between">
        <nav class="flex space-x-8 overflow-x-auto hide-scrollbar">
          <button
            *ngFor="
              let tab of tabService.tabsValue;
              let i = index;
              trackBy: trackByTabId
            "
            [class]="
              getActiveTabIndex() === i
                ? 'tab-button-active whitespace-nowrap'
                : 'tab-button whitespace-nowrap'
            "
            (click)="onTabChange(i)"
          >
            <div class="flex items-center space-x-2">
              <span class="whitespace-nowrap">{{
                tab?.name || "Operation " + (i + 1)
              }}</span>
              <div
                class="flex items-center space-x-1 ml-2"
                *ngIf="tab && tab.id"
              >
                <!-- Rename Button -->
                <button
                  class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 whitespace-nowrap"
                  (click)="$event.stopPropagation(); startRenameTab(tab.id)"
                  title="Rename tab"
                >
                  <lucide-icon [img]="EditIcon" class="w-4 h-4"></lucide-icon>
                </button>
                <!-- Delete Button -->
                <button
                  class="p-1 rounded hover:bg-red-100 dark:hover:bg-red-900 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 whitespace-nowrap"
                  (click)="$event.stopPropagation(); deleteTab(tab.id)"
                  title="Delete tab"
                >
                  <lucide-icon [img]="Trash2Icon" class="w-4 h-4"></lucide-icon>
                </button>
              </div>
            </div>
          </button>
        </nav>

        <!-- Add New Operation Button -->
        <button class="btn-primary" (click)="createTab()">
          <lucide-icon [img]="PlusIcon" class="w-5 h-5"></lucide-icon>
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div
      *ngFor="
        let tab of tabService.tabsValue;
        let i = index;
        trackBy: trackByTabId
      "
    >
      <div *ngIf="getActiveTabIndex() === i" class="space-y-6">
        <!-- Profile Selection -->
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Sync Profile
          </label>
          <div class="relative">
            <select
              class="select-field pr-10"
              [value]="tab?.selectedProfileIndex"
              (change)="onProfileChange($event, tab?.id)"
              [disabled]="!tab || !tab.id"
            >
              <option [value]="null">No profile selected</option>
              <option
                *ngFor="
                  let profile of appService.configInfo$.value.profiles;
                  let idx = index
                "
                [value]="idx"
              >
                {{ profile.name }}
              </option>
            </select>
            <div
              class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
            >
              <lucide-icon
                [img]="ChevronDownIcon"
                class="w-5 h-5 text-gray-400"
              ></lucide-icon>
            </div>
          </div>
        </div>

        <!-- Working Directory Section -->
        <div>
          <div class="flex items-center space-x-2 mb-3">
            <lucide-icon
              [img]="FolderOpenIcon"
              class="w-5 h-5 text-gray-600 dark:text-gray-400"
            ></lucide-icon>
            <span class="font-medium text-gray-700 dark:text-gray-300"
              >Working Directory</span
            >
          </div>
          <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <code class="text-sm text-gray-800 dark:text-gray-200">{{
              (appService.configInfo$ | async)?.working_dir
            }}</code>
          </div>
        </div>

        <!-- Status Display -->
        <div *ngIf="tab.currentAction">
          <div
            class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4"
          >
            <div
              class="bg-primary-600 h-2 rounded-full animate-pulse"
              style="width: 100%"
            ></div>
          </div>
          <div
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200"
          >
            <lucide-icon
              [img]="getActionIcon(tab.currentAction)"
              class="w-4 h-4 mr-2"
            ></lucide-icon>
            {{ getActionLabel(tab.currentAction) }}
          </div>
        </div>

        <!-- Console Output -->
        <div>
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <lucide-icon
                [img]="TerminalIcon"
                class="w-5 h-5 text-gray-600 dark:text-gray-400"
              ></lucide-icon>
              <span class="font-medium text-gray-700 dark:text-gray-300"
                >Console Output</span
              >
            </div>
            <button
              class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 whitespace-nowrap"
              (click)="clearTabOutput(tab.id)"
              title="Clear output"
            >
              <lucide-icon [img]="EraserIcon" class="w-5 h-5"></lucide-icon>
            </button>
          </div>
          <pre class="console-output">{{
            tab.data.join("\n") || "No output yet..."
          }}</pre>
        </div>

        <!-- Action Buttons -->
        <div *ngIf="validateTabProfileIndex(tab)">
          <div class="flex flex-wrap gap-3">
            <button
              [class]="
                tab.currentAction === Action.Pull
                  ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                  : 'btn-primary'
              "
              [disabled]="
                (!validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Pull) ||
                tab.isStopping
              "
              (click)="
                tab.currentAction !== Action.Pull
                  ? pullTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <div class="flex items-center space-x-2">
                <lucide-icon
                  [img]="
                    tab.isStopping
                      ? ClockIcon
                      : tab.currentAction === Action.Pull
                      ? StopCircleIcon
                      : DownloadIcon
                  "
                  class="w-5 h-5 mr-2"
                ></lucide-icon>
                {{
                  tab.isStopping
                    ? "Stopping..."
                    : tab.currentAction === Action.Pull
                    ? "Stop Pull"
                    : "Pull"
                }}
              </div>
            </button>

            <button
              [class]="
                tab.currentAction === Action.Push
                  ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                  : 'bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
              "
              [disabled]="
                !validateTabProfileIndex(tab) &&
                tab.currentAction !== Action.Push
              "
              (click)="
                tab.currentAction !== Action.Push
                  ? pushTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <div class="flex items-center space-x-2">
                <lucide-icon
                  [img]="
                    tab.currentAction === Action.Push
                      ? StopCircleIcon
                      : UploadIcon
                  "
                  class="w-5 h-5 mr-2"
                ></lucide-icon>
                {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
              </div>
            </button>

            <button
              [class]="
                tab.currentAction === Action.Bi
                  ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                  : 'btn-primary'
              "
              [disabled]="
                !validateTabProfileIndex(tab) && tab.currentAction !== Action.Bi
              "
              (click)="
                tab.currentAction !== Action.Bi
                  ? biTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <div class="flex items-center space-x-2">
                <lucide-icon
                  [img]="
                    tab.currentAction === Action.Bi
                      ? StopCircleIcon
                      : RefreshCwIcon
                  "
                  class="w-5 h-5 mr-2"
                ></lucide-icon>
                {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
              </div>
            </button>

            <button
              class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap"
              [disabled]="
                !validateTabProfileIndex(tab) &&
                tab.currentAction !== Action.BiResync
              "
              (click)="
                tab.currentAction !== Action.BiResync
                  ? biResyncTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <div class="flex items-center space-x-2">
                <lucide-icon
                  [img]="
                    tab.currentAction === Action.BiResync
                      ? StopCircleIcon
                      : RotateCcwIcon
                  "
                  class="w-5 h-5 mr-2"
                ></lucide-icon>
                {{
                  tab.currentAction === Action.BiResync
                    ? "Stop Resync"
                    : "Resync"
                }}
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Rename Tab Modal -->
<div
  *ngIf="showRenameDialog"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  (click)="cancelRename()"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-md mx-4"
    (click)="$event.stopPropagation()"
  >
    <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      Rename Tab
    </h2>

    <div class="mb-6">
      <label
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        Tab Name
      </label>
      <input
        type="text"
        class="input-field"
        [(ngModel)]="renameDialogData.newName"
        (keydown.enter)="confirmRename()"
        #renameDialogInput
      />
    </div>

    <div class="flex justify-end space-x-3">
      <button class="btn-secondary" (click)="cancelRename()">Cancel</button>
      <button class="btn-primary" (click)="confirmRename()">Rename</button>
    </div>
  </div>
</div>
